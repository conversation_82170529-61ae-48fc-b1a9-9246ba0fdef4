#include "data_collector.h"
#include "rolling_buffer.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "i2c_ms583730b.h"
#include "adc_ntc.h"

static const char *TAG = "COLLECTOR";

// 全局状态变量
static data_collector_state_t collector_state = DATA_COLLECTOR_STOPPED;
static uint8_t current_frequency = 5;
static esp_timer_handle_t sampling_timer = NULL;

// 性能统计
static uint32_t total_samples = 0;
static uint32_t failed_samples = 0;
//
static ms5837_t sensor; // 初始化 MS5837 传感器

// 使用ADC采样电路测温
// 返回温度值 x 10 (例：26.3°C 返回 263)
esp_err_t
get_temperature(int16_t *temp)
{
    esp_err_t ret;
    float temp_float = 0;
    ret = adc_ntc_read_chan0(&temp_float);
    if (ret == ESP_OK)
    {
        *temp = (int16_t)(temp_float * 10); // 转换为整型 x 10
        ESP_LOGW(TAG, "读取温度成功: %.1f°C (整型值: %d)", temp_float, *temp);
    }
    else
    {
        ESP_LOGW(TAG, "读取温度失败: %s", esp_err_to_name(ret));
        *temp = 0;
    }
    return ret;
}

// 使用MS5837取压力(I2C通讯)
// 返回压力值 x 10 (例：101.3kPa 返回 1013)
esp_err_t get_pressure(uint16_t *press)
{
    esp_err_t ret;
    float temp_float = 0, press_float = 0;
    ret = ms5837_get_data(&sensor, &temp_float, &press_float);
    if (ret == ESP_OK)
    {
        *press = (uint16_t)(press_float * 10); // 转换为整型 x 10
        ESP_LOGD(TAG, "MS5837 读取成功 压力: %.1f kPa (整型值: %d), 温度: %.1f°C",
                 press_float, *press, temp_float);
    }
    else
    {
        ESP_LOGE(TAG, "MS5837 读取失败 错误: %s", esp_err_to_name(ret));
        *press = 0;
    }
    return ret;
}

// 定时器回调函数
static void sampling_timer_callback(void *arg)
{
    sensor_data_t data;
    esp_err_t ret;
    bool sample_failed = false;

    total_samples++;

    // 采集温度数据
    ret = get_temperature(&data.temperature);
    if (ret != ESP_OK)
    {
        data.temperature = 0;
        sample_failed = true;
    }

    // 采集压力数据
    ret = get_pressure(&data.pressure);
    if (ret != ESP_OK)
    {
        data.pressure = 0;
        sample_failed = true;
    }

    if (sample_failed)
    {
        failed_samples++;
    }

    // 添加到滚动缓冲区
    rb_append(&data);

    // 实测高频日志输出很吃资源,严重影响性能

    // （仅在高频时）(仅做调试用)
    if (current_frequency > 1 && (total_samples % 10) == 0)
    {
        ESP_LOGI(TAG, "采集统计: 总计%lu次, 失败%lu次, 成功率%.1f%%",
                 total_samples, failed_samples,
                 (total_samples - failed_samples) * 100.0 / total_samples);
    }
}

esp_err_t data_collector_init(void)
{
    esp_err_t ret;

    // 初始化滚动缓冲区
    rb_init();

    // 初始化 MS5837 传感器
    ret = ms5837_init(&sensor);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize MS5837 sensor: %s", esp_err_to_name(ret));
    }

    // 初始化ADC
    ret = adc_ntc_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize ADC");
    }

    // 启动定时器
    ret = data_collector_start(current_frequency);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to start collector: %s", esp_err_to_name(ret));
    }

    collector_state = DATA_COLLECTOR_RUNNING;

    return ESP_OK;
}

esp_err_t data_collector_deinit(void)
{
    // 停止定时器
    data_collector_stop();

    // 清空缓冲区
    rb_clear();

    collector_state = DATA_COLLECTOR_STOPPED;
    current_frequency = 0;

    return ESP_OK;
}

esp_err_t data_collector_start(uint8_t frequency)
{
    if (frequency < 1 || frequency > 20)
    {
        ESP_LOGE(TAG, "无效的采集频率: %d (有效范围: 1-20)", frequency);
        return ESP_ERR_INVALID_ARG;
    }

    // 高频采集警告
    if (frequency > 10)
    {
        ESP_LOGW(TAG, "高频采集 %d Hz 可能影响BLE通信性能", frequency);
    }

    // 如果已经在运行，先停止
    if (collector_state == DATA_COLLECTOR_RUNNING)
    {
        data_collector_stop();
    }

    ESP_LOGI(TAG, "启动定时采集，频率: %d Hz", frequency);

    // 计算定时器周期（微秒）
    uint64_t period_us = 1000000 / frequency;

    // 创建定时器
    esp_timer_create_args_t timer_args = {
        .callback = sampling_timer_callback,
        .arg = NULL,
        .name = "sampling_timer"};

    esp_err_t ret = esp_timer_create(&timer_args, &sampling_timer);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "创建定时器失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 启动定时器
    ret = esp_timer_start_periodic(sampling_timer, period_us);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "启动定时器失败: %s", esp_err_to_name(ret));
        esp_timer_delete(sampling_timer);
        sampling_timer = NULL;
        return ret;
    }

    collector_state = DATA_COLLECTOR_RUNNING;
    current_frequency = frequency;

    ESP_LOGI(TAG, "定时采集已启动，周期: %llu us", period_us);
    return ESP_OK;
}

esp_err_t data_collector_stop(void)
{
    if (collector_state == DATA_COLLECTOR_STOPPED)
    {
        return ESP_OK; // 已经停止
    }

    ESP_LOGI(TAG, "停止定时采集");

    if (sampling_timer != NULL)
    {
        esp_timer_stop(sampling_timer);
        esp_timer_delete(sampling_timer);
        sampling_timer = NULL;
    }

    collector_state = DATA_COLLECTOR_STOPPED;
    current_frequency = 0;

    ESP_LOGI(TAG, "定时采集已停止");
    return ESP_OK;
}

data_collector_state_t data_collector_get_state(void)
{
    return collector_state;
}

uint8_t data_collector_get_frequency(void)
{
    return current_frequency;
}

int data_collector_get_latest_data(sensor_data_t *output, int n)
{
    return rb_get_latest_n(output, n);
}

void data_collector_clear_buffer(void)
{
    rb_clear();
}

int data_collector_get_buffer_count(void)
{
    return rb_count();
}
